import logging
from decimal import Decimal

from django.core.management.base import BaseCommand
from django.db.models import Sum

from wms.apps.inventories.models import DailyStockBalance, Stock
# from wms.apps.inventories.utils import bulk_update_daily_stock_balances

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Run the DailyStockBalance creation command.

    This command is to create all the stock's latest transaction balance on new DB.

    """

    help = "Create all DailyStockBalance on the new DB"

    def handle(self, *args, **options):
        """Main function to rebuild indexes"""
        all_stock = Stock.objects.all()
        # updated_daily_stock_balance_count = bulk_update_daily_stock_balances(stock_qs=all_stock, batch_size=1000)
        updated_daily_stock_balance_count = 0
        self.stdout.write(
            self.style.SUCCESS(f"Successfully updated {updated_daily_stock_balance_count} Number of DailyStockBalance.")
        )
        logger.info(f"{updated_daily_stock_balance_count} Number of DailyStockBalance Updated.")
